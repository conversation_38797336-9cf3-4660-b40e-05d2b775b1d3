#!/bin/sh

PYTHON_VERSION=$(python3 -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')

RELOAD=""
if [ -n "$LANGSMITH_LANGGRAPH_DESKTOP" ]; then
    echo "LANGSMITH_LANGGRAPH_DESKTOP is set. Running uvicorn with --reload flag..."
    RELOAD="--reload"
fi

if [ -n "$DD_API_KEY" ] && { [ "$PYTHON_VERSION" = "3.11" ] || [ "$PYTHON_VERSION" = "3.12" ]; }; then
    echo "WARNING: DD_API_KEY is set. Datadog tracing will wrap the entire server process."
    echo "         Only use this if you want Datadog as your primary tracing provider."
    echo "         Do NOT set DD_API_KEY if you want to use OpenTelemetry or another tracing backend."
    exec /app/datadog-init /dd_tracer/python/bin/ddtrace-run uvicorn langgraph_api.server:app --log-config /api/logging.json --host 0.0.0.0 --port $PORT --no-access-log --timeout-graceful-shutdown 3600 $RELOAD
else
    exec uvicorn langgraph_api.server:app --log-config /api/logging.json --host 0.0.0.0 --port $PORT --no-access-log --timeout-graceful-shutdown 3600 $RELOAD
fi
