.PHONY: lint format test test-watch test-postgres-license-noop test-postgres-license-jwt test-postgres-starlette test-postgres-watch start-api start-api-licensed start-api-starlette start-node-api start start-license start-postgres start-postgres-starlette build-3.11-postgres build-3.12-postgres build-3.11-postgres-licensed build-3.12-postgres-licensed build-node-20-postgres build-node-20-postgres-licensed build-node-22-postgres build-node-22-postgres-licensed build-postgres build-postgres-licensed check-base-imports test-unit-tests

lint:
	uv run --group executor ruff check .
	uv run --group executor ruff format . --diff
	uv run --group executor ty check --exclude "**/pb/**" --exclude "**/*_test.py" --exclude "**/test_*.py" --exclude "**/tests/**" --exclude "venv/**" --exclude ".venv/**" --exclude "build/**" --exclude "dist/**" .


format:
	uv run --group executor ruff check --select I --fix .
	uv run --group executor ruff format .

check-base-imports:
	DATABASE_URI="foo" REDIS_URI="bar" uv run python -c "from langgraph_runtime_postgres import *"

# dev commands

CWD := $(shell pwd)
LANGGRAPH_HTTP ?= ''
LANGGRAPH_AES_KEY ?= '1234567890123456'
ifeq ($(LANGGRAPH_HTTP),fastapi)
	HTTP_CONFIG := {"app": "../api/tests/graphs/my_router.py:app", "mount_prefix": "/my-cool/api"}
else
	HTTP_CONFIG := $(LANGGRAPH_HTTP)
endif

LANGGRAPH_STORE ?= ""
ifeq ($(LANGGRAPH_STORE),custom)
	STORE_CONFIG := {"path": "../api/tests/graphs/custom_store.py:generate_store"}
else
	STORE_CONFIG := {"index": {"dims": 500, "embed": "../api/tests/graphs/test_utils/embeddings.py:embeddings"}}
endif

IS_MINIMUM_DEPS ?= false
N_JOBS_PER_WORKER ?= 10
REDIS_CLUSTER ?= false
REDIS_URI ?= redis://localhost:6381

LANGSERVE_GRAPHS ?= '{"agent": "../api/tests/graphs/agent.py:graph", "agent_simple": "../api/tests/graphs/agent_simple.py:graph", "benchmark": "../api/tests/graphs/benchmark.py:graph", "config_graph": "../api/tests/graphs/config_graph.py:graph", "custom_lifespan": "../api/tests/graphs/my_router.py:graph", "other": "../api/tests/graphs/other.py:make_graph", "weather": "../api/tests/graphs/weather.py:mk_weather_graph", "searchy": "../api/tests/graphs/searchy.py:graph", "simple_runtime": "../api/tests/graphs/simple_runtime.py:graph", "single_node": "../api/tests/graphs/single_node.py:graph", "agent_interrupt": "../api/tests/graphs/agent_interrupt.py:graph"}'
LANGSERVE_GRAPHS_NODE ?= '{"agent": {"path": "../api/langgraph_api/js/tests/graphs/agent.mts:graph", "description": "agent"}, "nested": "../api/langgraph_api/js/tests/graphs/nested.mts:graph", "weather": "../api/langgraph_api/js/tests/graphs/weather.mts:graph", "error": "../api/langgraph_api/js/tests/graphs/error.mts:graph", "delay": "../api/langgraph_api/js/tests/graphs/delay.mts:graph", "dynamic": "../api/langgraph_api/js/tests/graphs/dynamic.mts:graph", "command": "../api/langgraph_api/js/tests/graphs/command.mts:graph", "agent_simple": "../api/langgraph_api/js/tests/graphs/agent_simple.mts:graph"}'
LANGSERVE_GRAPHS_NODE_LOCAL ?= '{"agent": {"path": "./agent.mts:graph", "description": "agent"}, "nested": "./nested.mts:graph", "weather": "./weather.mts:graph", "error": "./error.mts:graph", "delay": "./delay.mts:graph", "dynamic": "./dynamic.mts:graph", "command": "./command.mts:graph", "agent_simple": "./agent_simple.mts:graph"}'
# {**LANGSERVE_GRAPHS, **LANGSERVE_GRAPHS_NODE}
LANGSERVE_GRAPHS_UNION ?= '{"agent": {"path": "../api/langgraph_api/js/tests/graphs/agent.mts:graph", "description": "agent"}, "agent_simple": "../api/langgraph_api/js/tests/graphs/agent_simple.mts:graph", "benchmark": "../api/tests/graphs/benchmark.py:graph", "config_graph": "../api/tests/graphs/config_graph.py:graph", "custom_lifespan": "../api/tests/graphs/my_router.py:graph", "other": "../api/tests/graphs/other.py:make_graph", "weather": "../api/langgraph_api/js/tests/graphs/weather.mts:graph", "searchy": "../api/tests/graphs/searchy.py:graph", "simple_runtime": "../api/tests/graphs/simple_runtime.py:graph", "single_node": "../api/tests/graphs/single_node.py:graph", "nested": "../api/langgraph_api/js/tests/graphs/nested.mts:graph", "error": "../api/langgraph_api/js/tests/graphs/error.mts:graph", "delay": "../api/langgraph_api/js/tests/graphs/delay.mts:graph", "dynamic": "../api/langgraph_api/js/tests/graphs/dynamic.mts:graph", "command": "../api/langgraph_api/js/tests/graphs/command.mts:graph"}'

start-api:
	sleep 3 &&  REDIS_CLUSTER=$(REDIS_CLUSTER) \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	LANGGRAPH_THREAD_TTL='{"sweep_interval_minutes":0.05}' \
	LANGGRAPH_AES_KEY='$(LANGGRAPH_AES_KEY)' \
	N_JOBS_PER_WORKER=$(N_JOBS_PER_WORKER) \
	LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
	LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS) \
	LANGGRAPH_STORE='$(STORE_CONFIG)' \
	LANGSMITH_LANGGRAPH_API_VARIANT=test \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	MIGRATIONS_PATH=./migrations \
	PYTHONPATH=$(CWD):../license_noop \
	uv run uvicorn langgraph_api.server:app \
		--reload \
		--port 9123 \
		--log-config ../api/logging.json \
		--reload-exclude 'tests/integration_tests/**' \
		--reload-include './langgraph_runtime_postgres' \
		--reload-include '../api/langgraph_api' \
		--reload-include '../api/tests/graphs/*' \
		--no-access-log \
		--timeout-graceful-shutdown 3

start-queue:
	REDIS_CLUSTER=$(REDIS_CLUSTER) \
	LANGGRAPH_AES_KEY='$(LANGGRAPH_AES_KEY)' \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	N_JOBS_PER_WORKER=2 \
	LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
	LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS) \
	LANGGRAPH_STORE='$(STORE_CONFIG)' \
	LANGSMITH_LANGGRAPH_API_VARIANT=test \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	MIGRATIONS_PATH=./migrations \
	PYTHONPATH=$(CWD):../license_noop \
	uv run watchfiles --sigint-timeout 1 --filter=python -- 'uv run python -m langgraph_api.queue_entrypoint'

start-python-executor:
	LANGGRAPH_AES_KEY='$(LANGGRAPH_AES_KEY)' \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	N_JOBS_PER_WORKER=2 \
	LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
	LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS) \
	LANGGRAPH_STORE='$(STORE_CONFIG)' \
	LANGSMITH_LANGGRAPH_API_VARIANT=test \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	MIGRATIONS_PATH=./migrations \
	PYTHONPATH=$(CWD):../license_noop \
	uv run --group executor watchfiles --sigint-timeout 1 --filter=python -- \
		'uv run python -m langgraph_api.executor_entrypoint --grpc-port 8188 '

start-go-queue:
	cd ../runtime_go && \
	N_JOBS_PER_WORKER=$(N_JOBS_PER_WORKER) && \
	make start-queue && \
	cd ../storage_postgres


start-postgres:
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "make start-api"

start-api-licensed:
	sleep 3 &&  LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	LANGGRAPH_AES_KEY='$(LANGGRAPH_AES_KEY)' \
	N_JOBS_PER_WORKER=2 \
	LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
	LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS) \
	LANGSMITH_LANGGRAPH_API_VARIANT=test \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	MIGRATIONS_PATH=./migrations \
	PYTHONPATH=$(CWD):../license_jwt \
	uv run uvicorn langgraph_api.server:app --reload --port 9123 --log-config ../api/logging.json --reload-exclude 'tests/integration_tests/**' --reload-include './langgraph_runtime_postgres' --reload-include '../api/langgraph_api' --no-access-log --timeout-graceful-shutdown 3

start-api-auth:
	sleep 3 && LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	LANGGRAPH_AES_KEY='$(LANGGRAPH_AES_KEY)' \
	N_JOBS_PER_WORKER=2 \
	LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
	LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS) \
	LANGGRAPH_STORE='{"index": {"dims": 500, "embed": "../api/tests/graphs/test_utils/embeddings.py:embeddings"}}' \
	LANGGRAPH_AUTH='{"path": "../api/tests/graphs/jwt_auth.py:auth"}' \
	LANGSMITH_LANGGRAPH_API_VARIANT=test \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	MIGRATIONS_PATH=./migrations \
	PYTHONPATH=$(CWD):../license_noop \
	uv run uvicorn langgraph_api.server:app --reload --port 9123 --log-config ../api/logging.json --reload-exclude 'tests/integration_tests/**' --reload-include './langgraph_runtime_postgres' --reload-include '../api/langgraph_api' --no-access-log --timeout-graceful-shutdown 3

start-node-build:
	(cd ../api/langgraph_api/js/tests/graphs && \
		LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
		LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS_NODE_LOCAL) \
		LANGGRAPH_UI='{"agent": "./agent.ui.tsx"}' \
		LANGGRAPH_HTTP='{"app": "./http.mts:app"}' \
			tsx ../../build.mts)

start-node-api:
	sleep 3 && \
		LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
		N_JOBS_PER_WORKER=5 \
		LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
		LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS_NODE) \
		LANGGRAPH_UI='{"agent": "../api/langgraph_api/js/tests/graphs/agent.ui.tsx"}' \
		LANGGRAPH_HTTP='{"app": "../api/langgraph_api/js/tests/graphs/http.mts:app"}' \
		LANGSMITH_LANGGRAPH_API_VARIANT=test \
		LANGGRAPH_RUNTIME_EDITION=postgres \
		REDIS_URI=$(REDIS_URI) \
		DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
		MIGRATIONS_PATH=./migrations \
		PYTHONPATH=$(CWD):../license_noop \
			uv run uvicorn \
			langgraph_api.server:app \
			--reload \
			--port 9123 \
			--log-config ../api/logging.json \
			--reload-exclude 'tests/integration_tests/**' \
			--reload-include './langgraph_runtime_postgres' \
			--reload-include '../api/langgraph_api' \
			--no-access-log \
			--timeout-graceful-shutdown 3

start-node-api-nginx:
	sleep 2 && \
		LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
		N_JOBS_PER_WORKER=5 \
		LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
		LANGSERVE_GRAPHS_NODE=$(LANGSERVE_GRAPHS_NODE) \
		LANGSERVE_GRAPHS_PYTHON=$(LANGSERVE_GRAPHS_UNION) \
		LANGGRAPH_UI='{"agent": "../api/langgraph_api/js/tests/graphs/agent.ui.tsx"}' \
		LANGGRAPH_HTTP='{"app": "../api/langgraph_api/js/tests/graphs/http.mts:app"}' \
		LANGSMITH_LANGGRAPH_API_VARIANT=test \
		LANGGRAPH_RUNTIME_EDITION=postgres \
		REDIS_URI=$(REDIS_URI) \
		DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
		MIGRATIONS_PATH=./migrations \
		PYTHONPATH=$(CWD):../license_noop \
			./serve-nginx-local.sh

start-node-api-build:
	cd ../api/langgraph_api/js/tests/graphs && \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
	LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS_NODE_LOCAL) \
	LANGGRAPH_UI='{"agent": "./agent.ui.tsx"}' \
	LANGGRAPH_HTTP='{"app": "./http.mts:app"}' \
	LANGSMITH_LANGGRAPH_API_VARIANT=test \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	tsx ../../build.mts

start-node-api-auth:
	sleep 3 && \
		LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
		N_JOBS_PER_WORKER=5 \
		LANGGRAPH_CONFIG='{"agent": {"configurable": {"model_name": "openai"}}}' \
		LANGSERVE_GRAPHS=$(LANGSERVE_GRAPHS_NODE) \
		LANGGRAPH_UI='{"agent": "../api/langgraph_api/js/tests/graphs/agent.ui.tsx"}' \
		LANGGRAPH_AUTH='{"path": "../api/langgraph_api/js/tests/graphs/auth.mts:auth", "cache": {"cache_keys": ["authorization"], "ttl_seconds": 60, "max_size": 1000}}' \
		LANGSMITH_LANGGRAPH_API_VARIANT=test \
		LANGGRAPH_RUNTIME_EDITION=postgres \
		REDIS_URI=$(REDIS_URI) \
		DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
		MIGRATIONS_PATH=./migrations \
		PYTHONPATH=$(CWD):../license_noop \
			uv run uvicorn \
			langgraph_api.server:app \
			--reload \
			--port 9123 \
			--log-config ../api/logging.json \
			--reload-exclude 'tests/integration_tests/**' \
			--reload-include './langgraph_runtime_postgres' \
			--reload-include '../api/langgraph_api' \
			--no-access-log \
			--timeout-graceful-shutdown 3

start:
ifeq ($(REDIS_CLUSTER),true)
	npx concurrently --kill-others "docker compose -f ./compose.cluster.yml up --remove-orphans -V" "REDIS_CLUSTER=true REDIS_URI=redis://localhost:6379 make start-api"
else
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "make start-api"
endif

start-with-queue:
ifeq ($(REDIS_CLUSTER),true)
	npx concurrently --kill-others "docker compose -f ./compose.cluster.yml up --remove-orphans -V" "sleep 7 && N_JOBS_PER_WORKER=0 REDIS_CLUSTER=true REDIS_URI=redis://localhost:6379 make start-api" "sleep 7 && REDIS_URI=redis://redis-node-0:6379 make start-queue"
else
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "N_JOBS_PER_WORKER=0 make start-api" "sleep 5 && REDIS_URI=redis://localhost:6381 make start-queue"
endif

start-go:
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "N_JOBS_PER_WORKER=0 make start-api" "sleep 7 && make start-python-grpc-server" "sleep 7 && make start-go-queue"


start-postgres-auth:
ifeq ($(REDIS_CLUSTER),true)
	npx concurrently --kill-others "docker compose -f ./compose.cluster.yml up --remove-orphans -V" "REDIS_CLUSTER=true REDIS_URI=redis://redis-node-0:6379 make start-api-auth"
else
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "make start-api-auth"
endif

start-postgres-node:
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "make start-node-api"

start-postgres-nginx:
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "make start-node-api-nginx"

start-license:
	npx concurrently --kill-others "docker compose up --remove-orphans -V" "make start-api-licensed"

# Build commands

INCLUDE_EXECUTOR_DEPS ?= false

build-3.11-postgres:
	docker build \
	--build-arg PYTHON_VERSION='3.11' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=cloud \
	--build-arg INCLUDE_EXECUTOR_DEPS=$(INCLUDE_EXECUTOR_DEPS) \
	--build-context api=../api \
	--build-context license=../license_noop \
	--build-context storage=. \
	-t langchain/langgraph-api:3.11 .

build-3.12-postgres:
	docker build \
	--build-arg PYTHON_VERSION='3.12' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=cloud \
	--build-arg INCLUDE_EXECUTOR_DEPS=$(INCLUDE_EXECUTOR_DEPS) \
	--build-context api=../api \
	--build-context license=../license_noop \
	--build-context storage=. \
	-t langchain/langgraph-api:3.12 .

build-3.13-postgres:
	docker build \
	--build-arg PYTHON_VERSION='3.13' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=cloud \
	--build-arg INCLUDE_EXECUTOR_DEPS=$(INCLUDE_EXECUTOR_DEPS) \
	--build-context api=../api \
	--build-context license=../license_noop \
	--build-context storage=. \
	-t langchain/langgraph-api:3.13 .

build-3.11-postgres-licensed:
	docker build \
	--build-arg PYTHON_VERSION='3.11' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=licensed \
	--build-arg INCLUDE_EXECUTOR_DEPS=$(INCLUDE_EXECUTOR_DEPS) \
	--build-context api=../api \
	--build-context license=../license_jwt \
	--build-context storage=. \
	-t langchain/langgraph-api:3.11 .

build-3.12-postgres-licensed:
	docker build \
	--build-arg PYTHON_VERSION='3.12' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=licensed \
	--build-arg INCLUDE_EXECUTOR_DEPS=$(INCLUDE_EXECUTOR_DEPS) \
	--build-context api=../api \
	--build-context license=../license_jwt \
	--build-context storage=. \
	-t langchain/langgraph-api:3.12 .

build-3.13-postgres-licensed:
	docker build \
	--build-arg PYTHON_VERSION='3.13' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=licensed \
	--build-arg INCLUDE_EXECUTOR_DEPS=$(INCLUDE_EXECUTOR_DEPS) \
	--build-context api=../api \
	--build-context license=../license_jwt \
	--build-context storage=. \
	-t langchain/langgraph-api:3.13 .

build-node-20-postgres:
	docker build -f Dockerfile.node \
	--build-arg NODE_VERSION='20' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=cloud \
	--build-context api=../api \
	--build-context license=../license_noop \
	--build-context storage=. \
	-t langchain/langgraphjs-api:20 .

build-node-20-postgres-licensed:
	docker build -f Dockerfile.node \
	--build-arg NODE_VERSION='20' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=licensed \
	--build-context api=../api \
	--build-context license=../license_jwt \
	--build-context storage=. \
	-t langchain/langgraphjs-api:20 .

build-node-22-postgres:
	docker build -f Dockerfile.node \
	--build-arg NODE_VERSION='22' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=cloud \
	--build-context api=../api \
	--build-context license=../license_noop \
	--build-context storage=. \
	-t langchain/langgraphjs-api:22 .

build-node-22-postgres-licensed:
	docker build -f Dockerfile.node \
	--build-arg NODE_VERSION='22' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=licensed \
	--build-context api=../api \
	--build-context license=../license_jwt \
	--build-context storage=. \
	-t langchain/langgraphjs-api:22 .

# TODO: add to `build-postgres` targets (experimental for now)
build-node-22-postgres-nginx:
	docker build -f Dockerfile.node \
	--build-arg NODE_VERSION='22' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=cloud \
	--build-arg ENABLE_EXPERIMENTAL_JS_SERVER=true \
	--build-context api=../api \
	--build-context license=../license_noop \
	--build-context storage=. \
	-t langchain/langgraphjs-api:22 .

build-node-22-postgres-nginx-wolfi:
	docker build -f Dockerfile.node.wolfi \
	--build-arg NODE_VERSION='22' \
	--build-arg REVISION=$(REVISION) \
	--build-arg VARIANT=cloud \
	--build-arg ENABLE_EXPERIMENTAL_JS_SERVER=true \
	--build-context api=../api \
	--build-context license=../license_noop \
	--build-context storage=. \
	-t langchain/langgraphjs-api:22 .

build-postgres: build-3.11-postgres build-3.12-postgres build-3.13-postgres build-node-20-postgres build-node-22-postgres

build-postgres-licensed: build-3.11-postgres-licensed build-3.12-postgres-licensed build-3.13-postgres-licensed build-node-20-postgres-licensed build-node-22-postgres-licensed

# test commands

TEST ?= "../api/tests/"
USE_DISTRIBUTED_RUNTIME ?= false

test:
	USE_DISTRIBUTED_RUNTIME=$(USE_DISTRIBUTED_RUNTIME) \
	PYTHONPATH=$(CWD):../license_noop \
	IS_MINIMUM_DEPS=${IS_MINIMUM_DEPS} \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	uv run pytest --import-mode append -v ${TEST}

test-watch:
	USE_DISTRIBUTED_RUNTIME=$(USE_DISTRIBUTED_RUNTIME) \
	PYTHONPATH=$(CWD):../license_noop \
	IS_MINIMUM_DEPS=${IS_MINIMUM_DEPS} \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	uv run ptw . -- -x -vv --ff --import-mode append ${TEST}

test-postgres-license-noop:
	PYTHONPATH=$(CWD):../license_noop \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	uv run pytest --import-mode append -v ${TEST}

test-postgres-license-jwt:
	PYTHONPATH=$(CWD):../license_jwt \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	LANGGRAPH_CLOUD_LICENSE_KEY=lcl_test_license_key \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	uv run pytest --import-mode append -v "../license_jwt/license_tests/" ${TEST}

AUTH_TEST ?= "../api/tests/integration_tests/test_custom_auth.py"

test-auth:
	PYTHONPATH=$(CWD):../license_noop \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_AUTH='{"path": "../api/tests/graphs/jwt_auth.py:auth"}' \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	uv run pytest --import-mode append -v ${AUTH_TEST}

test-auth-watch:
	PYTHONPATH=$(CWD):../license_noop \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_AUTH='{"path": "../api/tests/graphs/jwt_auth.py:auth"}' \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	uv run ptw . -- -x -vv --ff --import-mode append ${AUTH_TEST}

test-watch-noop: test-watch

test-unit:
	USE_DISTRIBUTED_RUNTIME=$(USE_DISTRIBUTED_RUNTIME) \
	PYTHONPATH=$(CWD):../license_noop \
	IS_MINIMUM_DEPS=${IS_MINIMUM_DEPS} \
	LANGGRAPH_RUNTIME_EDITION=postgres \
	LANGGRAPH_HTTP='$(HTTP_CONFIG)' \
	REDIS_URI=$(REDIS_URI) \
	DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
	uv run pytest -v tests/unit_tests/
