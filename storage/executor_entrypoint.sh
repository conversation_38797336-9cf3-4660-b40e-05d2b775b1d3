#!/bin/sh

EXECUTOR_GRPC_PORT=${EXECUTOR_GRPC_PORT:-50051}
PYTHON_VERSION=$(python3 -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')

if [ -n "$DD_API_KEY" ] && { [ "$PYTHON_VERSION" = "3.11" ] || [ "$PYTHON_VERSION" = "3.12" ]; }; then
    echo "WARNING: DD_API_KEY is set. Datadog tracing will wrap the executor worker process."
    echo "         Only use this if you want Datadog as your primary tracing provider."
    echo "         Do NOT set DD_API_KEY if you want to use OpenTelemetry or another tracing backend."
    exec /app/datadog-init /dd_tracer/python/bin/ddtrace-run python -m langgraph_api.executor_entrypoint --grpc-port $EXECUTOR_GRPC_PORT
else
    exec python -m langgraph_api.executor_entrypoint --grpc-port $EXECUTOR_GRPC_PORT
fi
