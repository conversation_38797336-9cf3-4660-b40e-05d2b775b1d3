#!/bin/bash

# Spins up the Python and Node.js servers with an Nginx reverse proxy in front for local development.
# Nginx is containerized for convenience (using `host` mode networking),
# but the Python and Node.js servers are run outside of a container as usual.

# Expects configuration environment variables (LANGGRAPH_...) to be set.

set -xeo pipefail

cleanup() {
    kill $NODE_PID $UVICORN_PID $NGINX_PID
    wait $NODE_PID $UVICORN_PID $NGINX_PID
    docker rm -f nginx-local || true
    exit
}

trap cleanup SIGTERM SIGINT

# TODO: make UI relative paths work...
LANGGRAPH_UI="" PORT=9121 LANGSERVE_GRAPHS=$LANGSERVE_GRAPHS_NODE node langgraph-api-server/index.mts & NODE_PID=$!

LANGSERVE_GRAPHS=$LANGSERVE_GRAPHS_PYTHON uv run uvicorn \
    langgraph_api.server:app \
    --reload \
    --port 9122 \
    --log-config ../api/logging.json \
    --reload-exclude 'tests/integration_tests/**' \
    --reload-include './langgraph_runtime_postgres' \
    --reload-include '../api/langgraph_api' \
    --no-access-log \
    --timeout-graceful-shutdown 3 & UVICORN_PID=$!

docker run \
  --rm \
  --name nginx-local \
  -v $(pwd)/nginx/nginx.local.conf:/etc/nginx/nginx.conf:ro \
  -v $(pwd)/nginx/routes.conf:/etc/nginx/routes.conf:ro \
  -p 9123:9123 \
  nginx:1.29.1 & NGINX_PID=$!

echo "Started processes: Uvicorn ($UVICORN_PID), Node.js ($NODE_PID), Nginx in Docker ($NGINX_PID)"
wait
