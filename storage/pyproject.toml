[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph-storage-postgres"
version = "0.1.0"
description = ""
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "psycopg[binary]>=3.2.8",
    "croniter>=1.0.1",
    "psycopg-pool>=3.2.2",
    "langgraph-checkpoint-postgres>=2.0.17",
    "coredis>=4.21,<5",
    "langgraph>=0.3.27",
]

[project.scripts]
langgraph-verify-graphs = "langgraph_api.graph:verify_graphs"

[tool.hatch.build.targets.wheel]
packages = ["langgraph_runtime_postgres"]

[tool.hatch.version]
path = "langgraph_runtime_postgres/__init__.py"

[tool.uv]
default-groups = ["dev"]

[dependency-groups]
dev = [
    "ruff>=0.11.10",
    "codespell>=2.2.0",
    "pytest>=7.4.4",
    "anyio>=4.4.0",
    "pytest-watcher>=0.4.2",
    "langgraph-cli>=0.1.63",
    "langgraph-sdk>=0.2.0",
    "pytest-repeat>=0.9.3",
    "pytest-retry>=1.6.3",
    "passlib>=1.7.4",
    "pyjwt>=2.10.1",
    "pytest-httpserver>=1.1.0",
    "pycryptodome>=3.22.0",
    "uvloop>=0.21.0",
    "uvicorn>=0.34.2",
    "langgraph-api",
    "fastapi>=0.115.12",
    "langgraph-checkpoint-sqlite>=2.0.10",
    "ty",
]
executor = ["langgraph-executor==0.0.1a5"]

[tool.uv.sources]
langgraph-api = { path = "../api", editable = true }

[tool.ruff]
lint.select = ["F821", "E", "F", "UP", "TRY400", "B", "SIM", "I"]
lint.ignore = ["E501", "B008"]
extend-exclude = [".venv", "dist", "build", "__pycache__", "venv", "tests"]
target-version = "py311"

[tool.ty.rules]
possibly-unbound-attribute = "error"
possibly-unbound-implicit-call = "error"
possibly-unresolved-reference = "error"
unresolved-global = "error"
unsupported-base = "error"

[tool.pytest-watcher]
now = true
delay = 3
patterns = ["*.py", "../api/**/*.py"]

[tool.pytest.ini_options]
# --strict-markers will raise errors on unknown marks.
# https://docs.pytest.org/en/7.1.x/how-to/mark.html#raising-errors-on-unknown-marks
#
# https://docs.pytest.org/en/7.1.x/reference/reference.html
# --strict-config       any warnings encountered while parsing the `pytest`
#                       section of the configuration file raise errors.
addopts = "--strict-markers --strict-config --durations=5 -vv"
