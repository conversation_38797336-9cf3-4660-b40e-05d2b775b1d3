services:
  langgraph-postgres:
    image: pgvector/pgvector:pg16
    restart: on-failure
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    command: ["postgres", "-c", "shared_preload_libraries=vector"]
    healthcheck:
      test: pg_isready -U postgres
      start_period: 10s
      timeout: 1s
      retries: 5
  
  # Redis Cluster using bitnami - multiple nodes approach
  redis-node-0:
    image: bitnami/redis-cluster:7.2
    restart: on-failure
    ports:
      - "6379:6379"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_NODES=redis-node-0:6379 redis-node-1:6379 redis-node-2:6379 redis-node-3:6379 redis-node-4:6379 redis-node-5:6379
      - REDIS_CLUSTER_CREATOR=yes
      - REDIS_CLUSTER_REPLICAS=1
      - REDIS_CLUSTER_ANNOUNCE_IP=${HOSTNAME}
      - REDIS_CLUSTER_ANNOUNCE_PORT=6379
      - REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16379
    volumes:
      - redis-cluster-0-data:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis-node-1:
    image: bitnami/redis-cluster:7.2
    restart: on-failure
    ports:
      - "6380:6379"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_NODES=redis-node-0:6379 redis-node-1:6379 redis-node-2:6379 redis-node-3:6379 redis-node-4:6379 redis-node-5:6379
      - REDIS_CLUSTER_ANNOUNCE_IP=${HOSTNAME}
      - REDIS_CLUSTER_ANNOUNCE_PORT=6379
      - REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16379
    volumes:
      - redis-cluster-1-data:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis-node-2:
    image: bitnami/redis-cluster:7.2
    restart: on-failure
    ports:
      - "6381:6379"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_NODES=redis-node-0:6379 redis-node-1:6379 redis-node-2:6379 redis-node-3:6379 redis-node-4:6379 redis-node-5:6379
      - REDIS_CLUSTER_ANNOUNCE_IP=${HOSTNAME}
      - REDIS_CLUSTER_ANNOUNCE_PORT=6379
      - REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16379
    volumes:
      - redis-cluster-2-data:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis-node-3:
    image: bitnami/redis-cluster:7.2
    restart: on-failure
    ports:
      - "6382:6379"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_NODES=redis-node-0:6379 redis-node-1:6379 redis-node-2:6379 redis-node-3:6379 redis-node-4:6379 redis-node-5:6379
      - REDIS_CLUSTER_ANNOUNCE_IP=${HOSTNAME}
      - REDIS_CLUSTER_ANNOUNCE_PORT=6379
      - REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16379
    volumes:
      - redis-cluster-3-data:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis-node-4:
    image: bitnami/redis-cluster:7.2
    restart: on-failure
    ports:
      - "6383:6379"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_NODES=redis-node-0:6379 redis-node-1:6379 redis-node-2:6379 redis-node-3:6379 redis-node-4:6379 redis-node-5:6379
      - REDIS_CLUSTER_ANNOUNCE_IP=${HOSTNAME}
      - REDIS_CLUSTER_ANNOUNCE_PORT=6379
      - REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16379
    volumes:
      - redis-cluster-4-data:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis-node-5:
    image: bitnami/redis-cluster:7.2
    restart: on-failure
    ports:
      - "6384:6379"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_NODES=redis-node-0:6379 redis-node-1:6379 redis-node-2:6379 redis-node-3:6379 redis-node-4:6379 redis-node-5:6379
      - REDIS_CLUSTER_ANNOUNCE_IP=${HOSTNAME}
      - REDIS_CLUSTER_ANNOUNCE_PORT=6379
      - REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16379
    volumes:
      - redis-cluster-5-data:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s


volumes:
  redis-cluster-0-data:
  redis-cluster-1-data:
  redis-cluster-2-data:
  redis-cluster-3-data:
  redis-cluster-4-data:
  redis-cluster-5-data:
