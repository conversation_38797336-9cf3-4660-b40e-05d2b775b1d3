# 项目启动

## 项目概述
这是一个LangGraph API服务器项目，使用Python和PostgreSQL作为后端存储。

## 启动方式

### 1. 最简单的启动方式
项目提供了便捷的Make命令：

```bash
cd storage
make start
```

这个命令会：
- 启动PostgreSQL和Redis容器（通过docker-compose）
- 启动Python API服务器（不在容器中运行）

### 2. 手动启动步骤

如果你想完全不使用Docker，需要：

#### 步骤1：安装依赖
```bash
cd storage
# 安装Python依赖
uv sync
```

#### 步骤2：启动外部服务
你需要手动启动PostgreSQL和Redis：
- PostgreSQL: 端口5433，数据库名postgres，用户名/密码都是postgres
- Redis: 端口6381

#### 步骤3：启动API服务器
```bash
cd storage
make start-api
```

或者直接运行：
```bash
cd storage
LANGGRAPH_RUNTIME_EDITION=postgres \
LANGGRAPH_AES_KEY='1234567890123456' \
REDIS_URI=redis://localhost:6381 \
DATABASE_URI=postgres://postgres:postgres@localhost:5433/postgres?sslmode=disable \
MIGRATIONS_PATH=./migrations \
uv run uvicorn langgraph_api.server:app --reload --port 9123
```

## 访问方式

启动成功后，服务器会运行在：
- **地址**: `127.0.0.1:9123` 或 `localhost:9123`
- **端口**: 9123

你可以：
1. 在浏览器中访问 `http://localhost:9123`
2. 在LangGraph Studio中连接到这个地址
3. 通过API客户端访问RESTful接口

## 项目结构说明

````toml path=storage/pyproject.toml mode=EXCERPT
[project]
name = "langgraph-storage-postgres"
version = "0.1.0"
description = ""
requires-python = ">=3.11"
dependencies = [
    "psycopg[binary]>=3.2.8",
    "croniter>=1.0.1",
    "psycopg-pool>=3.2.2",
    "langgraph-checkpoint-postgres>=2.0.17",
    "coredis>=4.21,<5",
    "langgraph>=0.3.27",
]
````

这个项目使用：
- Python 3.11+
- PostgreSQL作为主数据库
- Redis作为缓存
- uvicorn作为ASGI服务器
- uv作为包管理器

总结：项目完全可以不使用Docker镜像启动，推荐使用`make start`命令，它会自动处理依赖服务的启动和API服务器的配置。
