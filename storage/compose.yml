services:
  langgraph-postgres:
    image: pgvector/pgvector:pg16
    restart: on-failure
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    command: ["postgres", "-c", "shared_preload_libraries=vector"]
    healthcheck:
      test: pg_isready -U postgres
      start_period: 10s
      timeout: 1s
      retries: 5
  langgraph-redis:
    image: redis:6
    restart: on-failure
    ports:
      - "6381:6379"
