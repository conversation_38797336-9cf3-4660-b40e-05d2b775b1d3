import { spawnServer } from "@langchain/langgraph-api";

// TODO: add another version of `spawnServer` that doesn't live reload?

await spawnServer(
  {
    host: "0.0.0.0",
    port: process.env.PORT || "2024",
    nJobsPerWorker: process.env.N_JOBS_PER_WORKER || "1",
  },
  {
    config: {
      graphs: JSON.parse(process.env.LANGSERVE_GRAPHS || "{}"),
      ui: JSON.parse(process.env.LANGGRAPH_UI || "{}"),
      ui_config: JSON.parse(process.env.LANGGRAPH_UI_CONFIG || "{}"),
      auth: JSON.parse(process.env.LANGGRAPH_AUTH || "{}"),
    },
    env: process.env,
    hostUrl: `http://localhost:${process.env.JS_PORT}`,
  },
  {
    pid: process.pid,
    projectCwd: process.cwd(),
  },
);
