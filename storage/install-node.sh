#!/bin/sh
export VCPKG_FORCE_SYSTEM_BINARIES=1

# Detect the operating system
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo "$ID"
    elif command -v apk >/dev/null 2>&1; then
        echo "alpine"
    elif command -v apt-get >/dev/null 2>&1; then
        echo "debian"
    else
        echo "unknown"
    fi
}

OS_TYPE=$(detect_os)
echo "Detected OS: $OS_TYPE"

# Install essential dependencies based on OS
install_dependencies() {
    case "$OS_TYPE" in
        "wolfi"|"alpine")
            echo "Installing dependencies for Wolfi/Alpine..."
            apk update && apk add --no-cache \
                git \
                curl \
                build-base \
                pkgconfig \
                cmake \
                zip \
                unzip \
                tar
            ;;
        "debian"|"ubuntu")
            echo "Installing dependencies for Debian/Ubuntu..."
            apt-get update && apt-get install --no-install-recommends -y \
                git \
                curl \
                build-essential \
                pkg-config \
                cmake \
                zip \
                unzip \
                tar
            ;;
        *)
            echo "Unsupported OS: $OS_TYPE"
            exit 1
            ;;
    esac
}

# Install Node.js based on OS
install_nodejs() {
    case "$OS_TYPE" in
        "wolfi"|"alpine")
            echo "Installing Node.js for Wolfi/Alpine..."
            # For Wolfi/Alpine, we can use apk or direct download
            apk add --no-cache nodejs npm
            ;;
        "debian"|"ubuntu")
            echo "Installing Node.js for Debian/Ubuntu..."
            curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION:-20}.x | bash -
            apt-get update && apt-get install --no-install-recommends -y nodejs
            ;;
        *)
            echo "Unsupported OS for Node.js installation: $OS_TYPE"
            exit 1
            ;;
    esac
}

# Clean up based on OS
cleanup() {
    case "$OS_TYPE" in
        "wolfi"|"alpine")
            echo "Cleaning up for Wolfi/Alpine..."
            rm -rf /var/cache/apk/* /root/.npm
            ;;
        "debian"|"ubuntu")
            echo "Cleaning up for Debian/Ubuntu..."
            rm -rf /usr/local/share/.cache /var/lib/apt/lists/* /root/.npm
            ;;
    esac
}

# Main installation flow
install_dependencies
install_nodejs

# Install package managers
echo "Before: corepack version => $(corepack --version || echo 'not installed')"
npm install -g tsx yarn pnpm bun corepack@~0.32.0
echo "After : corepack version => $(corepack --version)"
corepack enable

# Conditional JS runtime installation
if [ "$SKIP_JS_RUNTIME_INSTALL" = "true" ]; then
    echo "Skipping JS runtime install"
else
    cd /api/langgraph_api/js && yarn install --frozen-lockfile --production
fi

if [ "$ENABLE_EXPERIMENTAL_JS_SERVER" = "true" ]; then
    echo "Installing experimental JS server"
    cd /storage/langgraph-api-server
    yarn install --frozen-lockfile --production
fi

# Clean up cache to reduce image size
cleanup
